"""
大模型配置模块
"""

from openai import OpenAI
import chromadb.utils.embedding_functions as embedding_functions
from typing import List, Union
from chromadb.api.types import EmbeddingFunction, Embeddings

# 聊天模型配置
CHAT_API_KEY = "sk-ab756c213e1248aea064b2e49ad24de8"
CHAT_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
CHAT_MODEL = "qwen-max-latest"

# 嵌入模型配置
EMBEDDING_API_KEY = "sk-ozycqmzxpuvwrebozcrpuizizupfeamkomtyvcfhgpgeszyi"
EMBEDDING_BASE_URL = "https://api.siliconflow.cn/v1"
EMBEDDING_MODEL = "BAAI/bge-m3"
EMBEDDING_DIMENSIONS = 1024

# 嵌入API批处理大小限制
EMBEDDING_BATCH_SIZE = 32  # 设置为32以确保不超过API限制

CHAT_CLIENT = OpenAI(api_key=CHAT_API_KEY, base_url=CHAT_BASE_URL)
EMBEDDING_CLIENT = OpenAI(api_key=EMBEDDING_API_KEY, base_url=EMBEDDING_BASE_URL)

class BatchLimitedEmbeddingFunction:

    def __init__(self, api_key: str, api_base: str, model_name: str,
                 dimensions: int, batch_size: int = EMBEDDING_BATCH_SIZE):
        self.batch_size = batch_size
        self.base_function = embedding_functions.OpenAIEmbeddingFunction(
            api_key=api_key,
            api_base=api_base,
            model_name=model_name,
            dimensions=dimensions
        )

    def __call__(self, input: List[str]) -> Embeddings:
        if len(input) <= self.batch_size:
            # 如果输入大小在限制内，直接调用
            return self.base_function(input)

        # 如果输入大小超过限制，分批处理
        all_embeddings = []
        for i in range(0, len(input), self.batch_size):
            batch = input[i:i + self.batch_size]
            batch_embeddings = self.base_function(batch)
            all_embeddings.extend(batch_embeddings)

        return all_embeddings

# ==================== 客户端创建函数 ====================

def get_chat_client() -> OpenAI:
    return CHAT_CLIENT

def get_embedding_client() -> OpenAI:
    return EMBEDDING_CLIENT

def get_openai_client() -> OpenAI:
    return CHAT_CLIENT

def get_openai_embedding_function():
    return BatchLimitedEmbeddingFunction(
        api_key=EMBEDDING_API_KEY,
        api_base=EMBEDDING_BASE_URL,
        model_name=EMBEDDING_MODEL,
        dimensions=EMBEDDING_DIMENSIONS,
        batch_size=EMBEDDING_BATCH_SIZE
    )

def create_chat_completion(messages, model: str = CHAT_MODEL, **kwargs):
    return CHAT_CLIENT.chat.completions.create(
        model=model,
        messages=messages,
        **kwargs,
    )

def create_embeddings(input_texts, model: str = EMBEDDING_MODEL, dimensions: int = EMBEDDING_DIMENSIONS, **kwargs):
    return EMBEDDING_CLIENT.embeddings.create(
        model=model,
        dimensions=dimensions,
        input=input_texts,
        **kwargs,
    )